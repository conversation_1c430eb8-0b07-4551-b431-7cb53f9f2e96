import React, { useState, useMemo } from 'react'
import { Fa<PERSON><PERSON>ch, Fa<PERSON><PERSON><PERSON>, FaSort, FaTh, FaList, FaChevronDown, FaTimes } from 'react-icons/fa'
import ShopCard from '../ShoppingCard/ShopCard'

const EnhancedShopLayout = ({
  products = [],
  title = "Products",
  heroImage,
  heroTitle,
  heroDescription,
  showFilters = true,
  showSearch = true,
  showSorting = true,
  showViewToggle = true,
  categories = []
}) => {
  // State management
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [priceRange, setPriceRange] = useState({ min: 0, max: 1000 })
  const [sortBy, setSortBy] = useState('name')
  const [sortOrder, setSortOrder] = useState('asc')
  const [viewMode, setViewMode] = useState('grid')
  const [showFiltersPanel, setShowFiltersPanel] = useState(false)

  // Filter and sort products
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter(product => {
      // Search filter
      const matchesSearch = product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description?.toLowerCase().includes(searchTerm.toLowerCase())

      // Category filter
      const matchesCategory = selectedCategory === 'all' ||
                             product.category === selectedCategory ||
                             product.title.toLowerCase().includes(selectedCategory.toLowerCase())

      // Price filter
      const price = parseFloat(product.price.replace('$', ''))
      const matchesPrice = price >= priceRange.min && price <= priceRange.max

      return matchesSearch && matchesCategory && matchesPrice
    })

    // Sort products
    filtered.sort((a, b) => {
      let aValue, bValue

      switch (sortBy) {
        case 'price':
          aValue = parseFloat(a.price.replace('$', ''))
          bValue = parseFloat(b.price.replace('$', ''))
          break
        case 'name':
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        case 'rating':
          aValue = a.rating || 4.5
          bValue = b.rating || 4.5
          break
        default:
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    return filtered
  }, [products, searchTerm, selectedCategory, priceRange, sortBy, sortOrder])

  // Get unique categories from products
  const availableCategories = useMemo(() => {
    const cats = new Set(['all'])
    products.forEach(product => {
      if (product.category) cats.add(product.category)
    })
    return Array.from(cats)
  }, [products])

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCategory('all')
    setPriceRange({ min: 0, max: 1000 })
    setSortBy('name')
    setSortOrder('asc')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      {(heroImage || heroTitle) && (
        <section className="bg-white">
          <div className="container mx-auto px-4 py-10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              {heroImage && (
                <div className="flex justify-center">
                  <img
                    className="h-80 object-contain"
                    src={heroImage}
                    alt={heroTitle || title}
                  />
                </div>
              )}
              <div className="flex flex-col justify-center">
                <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-4">
                  {heroTitle || title}
                </h1>
                {heroDescription && (
                  <p className="text-xl text-gray-600 leading-relaxed">
                    {heroDescription}
                  </p>
                )}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Search and Filter Bar */}
      <section className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search */}
            {showSearch && (
              <div className="relative flex-1 max-w-md">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
                />
              </div>
            )}

            {/* Controls */}
            <div className="flex items-center gap-4">
              {/* Filter Toggle */}
              {showFilters && (
                <button
                  onClick={() => setShowFiltersPanel(!showFiltersPanel)}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  <FaFilter />
                  <span>Filters</span>
                  <FaChevronDown className={`transform transition-transform ${showFiltersPanel ? 'rotate-180' : ''}`} />
                </button>
              )}

              {/* Sort */}
              {showSorting && (
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [field, order] = e.target.value.split('-')
                    setSortBy(field)
                    setSortOrder(order)
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
                >
                  <option value="name-asc">Name A-Z</option>
                  <option value="name-desc">Name Z-A</option>
                  <option value="price-asc">Price Low-High</option>
                  <option value="price-desc">Price High-Low</option>
                  <option value="rating-desc">Rating High-Low</option>
                </select>
              )}

              {/* View Toggle */}
              {showViewToggle && (
                <div className="flex bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white shadow' : ''}`}
                  >
                    <FaTh />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded ${viewMode === 'list' ? 'bg-white shadow' : ''}`}
                  >
                    <FaList />
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Filters Panel */}
          {showFiltersPanel && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
                  >
                    {availableCategories.map(cat => (
                      <option key={cat} value={cat}>
                        {cat === 'all' ? 'All Categories' : cat.charAt(0).toUpperCase() + cat.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Price Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                  <div className="flex gap-2">
                    <input
                      type="number"
                      placeholder="Min"
                      value={priceRange.min}
                      onChange={(e) => setPriceRange(prev => ({ ...prev, min: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
                    />
                    <input
                      type="number"
                      placeholder="Max"
                      value={priceRange.max}
                      onChange={(e) => setPriceRange(prev => ({ ...prev, max: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Clear Filters */}
                <div className="flex items-end">
                  <button
                    onClick={clearFilters}
                    className="w-full px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors flex items-center justify-center gap-2"
                  >
                    <FaTimes />
                    Clear Filters
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Products Section */}
      <section className="container mx-auto px-4 py-8">
        {/* Results Info */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {title} ({filteredAndSortedProducts.length} products)
          </h2>
        </div>

        {/* Products Grid/List */}
        {filteredAndSortedProducts.length > 0 ? (
          <div className={`grid gap-6 ${
            viewMode === 'grid'
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          }`}>
            {filteredAndSortedProducts.map((product) => (
              <ShopCard
                key={product.id}
                id={product.id}
                title={product.title}
                img={product.img}
                price={product.price}
                description={product.description}
                rating={product.rating}
                originalPrice={product.originalPrice}
                isNew={product.isNew}
                isSale={product.isSale}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or filter criteria</p>
            <button
              onClick={clearFilters}
              className="px-6 py-2 bg-[#575CEE] text-white rounded-lg hover:bg-[#4a4fd1] transition-colors"
            >
              Clear All Filters
            </button>
          </div>
        )}
      </section>
    </div>
  )
}

export default EnhancedShopLayout
