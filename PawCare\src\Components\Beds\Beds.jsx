import React from 'react'
import EnhancedShopLayout from '../Shop/EnhancedShopLayout'


const dogBeds = [
  {
    id: 1,
    title: 'Orthopedic Dog Bed',
    img: 'https://tse3.mm.bing.net/th?id=OIP._CO04FuLYjx1dXBDWGpvOAHaE8&pid=Api&P=0&h=220',
    price: '$89.99',
    description: 'Memory foam orthopedic bed for large dogs'
  },
  {
    id: 2,
    title: 'Donut Cuddler',
    img: 'http://www.hkfoamus.com/images/thumbs/0000136_dog-bed.png',
    price: '$49.99',
    description: 'Soft, comfortable donut cuddler for small to medium dogs'
  },
  {
    id: 3,
    title: 'Elevated Dog Bed',
    img: 'https://snoozerpetproducts.com/wp-content/uploads/2014/03/Square_Dog_Bed_L_DarkChoc.png',
    price: '$39.99',
    description: 'Elevated cooling bed for outdoor use'
  },
  {
    id: 4,
    title: 'Luxury Dog Sofa',
    img: 'https://tse3.mm.bing.net/th?id=OIP.T-aMXUFiquELDU_8P_sn7wHaHa&pid=Api&P=0&h=220',
    price: '$129.99',
    description: 'Premium sofa-style bed for dogs'
  },
  {
    id: 5,
    title: 'Heated Dog Bed',
    img: 'https://tse3.mm.bing.net/th?id=OIP.9DntA--mwU_adCQLOuX56wHaEu&pid=Api&P=0&h=220',
    price: '$69.99',
    description: 'Self-warming bed for cold weather'
  }
];


const catBeds = [
  {
    id: 101,
    title: 'Cat Cave Bed',
    img: 'https://tse3.mm.bing.net/th?id=OIP.QtCixNY68nMwo1Gh_57JEAHaHa&pid=Api&P=0&h=220',
    price: '$29.99',
    description: 'Cozy cave bed for cats who like to hide'
  },
  {
    id: 102,
    title: 'Window Perch',
    img: 'https://wallpapers.com/images/hd/unicorn-themed-cat-bed-fo53w6fdl2hjkulr.png',
    price: '$24.99',
    description: 'Window-mounted perch for sunbathing cats'
  },
  {
    id: 103,
    title: 'Heated Cat Pad',
    img: 'https://png.pngtree.com/png-vector/20231115/ourmid/pngtree-dog-or-cat-bed-furniture-png-image_10597305.png',
    price: '$34.99',
    description: 'Self-heating pad for cats'
  },
  {
    id: 104,
    title: 'Cat Tree with Bed',
    img: 'https://tse4.mm.bing.net/th?id=OIP._E7VQwpTV_LA5k1qRS6xvgAAAA&pid=Api&P=0&h=220',
    price: '$79.99',
    description: 'Multi-level cat tree with built-in beds'
  },
  {
    id: 105,
    title: 'Luxury Cat Hammock',
    img: 'https://i.pinimg.com/originals/9f/32/18/9f3218d319b91a3621d92b6e28139005.png',
    price: '$19.99',
    description: 'Comfortable hammock for cats'
  }
];

const specialtyBeds = [
  {
    id: 201,
    title: 'Orthopedic Senior Bed',
    img: 'https://catorcat.com/wp-content/uploads/2021/02/green-cat-bed.png',
    price: '$99.99',
    description: 'Extra supportive bed for senior pets'
  },
  {
    id: 202,
    title: 'Anxiety Relief Bed',
    img: 'https://static.vecteezy.com/system/resources/thumbnails/014/166/556/small_2x/isometric-bedroom-3d-render-png.png',
    price: '$59.99',
    description: 'Calming bed for anxious pets'
  },
  {
    id: 203,
    title: 'Travel Pet Bed',
    img: 'https://amarpet-space.sgp1.digitaloceanspaces.com/production/d68a18275455ae3eaa2c291eebb46e6d/Bat-cat-bed.png',
    price: '$45.99',
    description: 'Portable bed for traveling with pets'
  },
  {
    id: 204,
    title: 'Outdoor Waterproof Bed',
    img: 'https://static.vecteezy.com/system/resources/thumbnails/009/585/332/small_2x/minimal-3d-illustration-of-wicker-cat-house-front-view-free-png.png',
    price: '$69.99',
    description: 'Waterproof bed for outdoor use'
  }
];

const Beds = () => {
  // Combine all bed products with enhanced data
  const allBedProducts = [
    ...dogBeds.map(product => ({
      ...product,
      category: 'dog-beds',
      rating: 4.4 + Math.random() * 0.6,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.2).toFixed(2)}` : null
    })),
    ...catBeds.map(product => ({
      ...product,
      category: 'cat-beds',
      rating: 4.5 + Math.random() * 0.5,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.15).toFixed(2)}` : null
    })),
    ...specialtyBeds.map(product => ({
      ...product,
      category: 'specialty-beds',
      rating: 4.6 + Math.random() * 0.4,
      isNew: Math.random() > 0.6,
      isSale: Math.random() > 0.8,
      originalPrice: Math.random() > 0.6 ? `$${(parseFloat(product.price.replace('$', '')) * 1.25).toFixed(2)}` : null
    }))
  ]

  return (
    <>
      <EnhancedShopLayout
        products={allBedProducts}
        title="Pet Beds & Comfort"
        heroImage="https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=800&h=600&fit=crop&crop=center"
        heroTitle="Comfortable Pet Beds"
        heroDescription="Find the perfect bed for your furry friend. We offer a wide selection of comfortable, durable, and stylish beds for dogs and cats of all sizes."
        showFilters={true}
        showSearch={true}
        showSorting={true}
        showViewToggle={true}
        categories={['dog-beds', 'cat-beds', 'specialty-beds']}
      />
    </>
  )
}

export default Beds
