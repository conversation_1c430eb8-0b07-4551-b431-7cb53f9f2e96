import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import {
  FaPaw,
  FaDog,
  FaCat,
  FaBed,
  FaGamepad,
  FaBath,
  FaDrumstickBite,
  FaCookie,
  FaCouch,
  FaChevronRight,
  FaHeart
} from 'react-icons/fa'
import cat from '../../assets/imgs/cat.png'
import dog1 from '../../assets/imgs/dog1.png'

function PetCategories() {
  const [hoveredCategory, setHoveredCategory] = useState(null)

  // Define all categories with their routes, icons, and descriptions
  const categories = [
    {
      id: 'cats',
      name: 'Cats',
      route: '/CatShop',
      icon: FaCat,
      image: cat,
      description: 'Everything for your feline friends',
      color: 'from-purple-500 to-pink-500',
      bgColor: 'bg-gradient-to-br from-purple-500 to-pink-500'
    },
    {
      id: 'dogs',
      name: 'Dogs',
      route: '/shopview',
      icon: FaDog,
      image: dog1,
      description: 'Premium products for your loyal companions',
      color: 'from-blue-500 to-indigo-600',
      bgColor: 'bg-gradient-to-br from-blue-500 to-indigo-600'
    },
    {
      id: 'beds',
      name: 'Beds & Comfort',
      route: '/beds',
      icon: FaBed,
      image: null,
      description: 'Cozy sleeping solutions for pets',
      color: 'from-green-500 to-teal-500',
      bgColor: 'bg-gradient-to-br from-green-500 to-teal-500'
    },
    {
      id: 'toys',
      name: 'Toys & Play',
      route: '/toys',
      icon: FaGamepad,
      image: null,
      description: 'Fun and engaging toys for active pets',
      color: 'from-yellow-500 to-orange-500',
      bgColor: 'bg-gradient-to-br from-yellow-500 to-orange-500'
    },
    {
      id: 'food',
      name: 'Food & Nutrition',
      route: '/food',
      icon: FaDrumstickBite,
      image: null,
      description: 'Nutritious meals for healthy pets',
      color: 'from-red-500 to-pink-500',
      bgColor: 'bg-gradient-to-br from-red-500 to-pink-500'
    },
    {
      id: 'treats',
      name: 'Treats & Snacks',
      route: '/treats',
      icon: FaCookie,
      image: null,
      description: 'Delicious rewards and training treats',
      color: 'from-amber-500 to-yellow-500',
      bgColor: 'bg-gradient-to-br from-amber-500 to-yellow-500'
    },
    {
      id: 'grooming',
      name: 'Bath & Grooming',
      route: '/bath',
      icon: FaBath,
      image: null,
      description: 'Keep your pets clean and healthy',
      color: 'from-cyan-500 to-blue-500',
      bgColor: 'bg-gradient-to-br from-cyan-500 to-blue-500'
    },
    {
      id: 'furniture',
      name: 'Furniture & Accessories',
      route: '/furniture',
      icon: FaCouch,
      image: null,
      description: 'Stylish furniture and accessories',
      color: 'from-gray-600 to-gray-700',
      bgColor: 'bg-gradient-to-br from-gray-600 to-gray-700'
    }
  ]

  return (
    <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <FaPaw className="text-[#575CEE] text-3xl mr-3" />
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900">
              Pet Categories
            </h2>
            <FaPaw className="text-[#575CEE] text-3xl ml-3" />
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover everything your beloved pets need, from premium food to cozy beds and fun toys
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-[#575CEE] to-purple-500 mx-auto mt-6 rounded-full"></div>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {categories.map((category) => {
            const IconComponent = category.icon
            return (
              <Link
                key={category.id}
                to={category.route}
                className="group block"
                onMouseEnter={() => setHoveredCategory(category.id)}
                onMouseLeave={() => setHoveredCategory(null)}
                role="button"
                tabIndex={0}
                aria-label={`Browse ${category.name} - ${category.description}`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    window.location.href = category.route
                  }
                }}
              >
                <div className={`
                  relative overflow-hidden rounded-2xl shadow-lg transition-all duration-500 ease-out
                  transform group-hover:scale-105 group-hover:shadow-2xl
                  ${category.bgColor}
                  h-64 sm:h-72 lg:h-80
                  focus:outline-none focus:ring-4 focus:ring-[#575CEE] focus:ring-opacity-50
                `}>
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                    <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
                  </div>

                  {/* Content Container */}
                  <div className="relative h-full flex flex-col justify-between p-6">
                    {/* Top Section - Icon/Image */}
                    <div className="flex justify-center items-center flex-1">
                      {category.image ? (
                        <div className="relative">
                          <img
                            src={category.image}
                            alt={`${category.name} category`}
                            className={`
                              w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 object-contain
                              transition-transform duration-500 ease-out
                              ${hoveredCategory === category.id ? 'scale-110 rotate-6' : ''}
                            `}
                          />
                          {hoveredCategory === category.id && (
                            <div className="absolute -top-2 -right-2 text-white">
                              <FaHeart className="text-lg animate-pulse" />
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="relative">
                          <IconComponent
                            className={`
                              text-white text-5xl sm:text-6xl lg:text-7xl
                              transition-all duration-500 ease-out
                              ${hoveredCategory === category.id ? 'scale-110 rotate-12' : ''}
                            `}
                          />
                          {hoveredCategory === category.id && (
                            <div className="absolute -top-2 -right-2 text-white">
                              <FaHeart className="text-lg animate-pulse" />
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Bottom Section - Text */}
                    <div className="text-center">
                      <h3 className="text-white font-bold text-lg sm:text-xl lg:text-2xl mb-2 group-hover:text-yellow-200 transition-colors duration-300">
                        {category.name}
                      </h3>
                      <p className={`
                        text-white/90 text-sm sm:text-base leading-relaxed
                        transition-all duration-500 ease-out
                        ${hoveredCategory === category.id ? 'opacity-100 transform translate-y-0' : 'opacity-80'}
                      `}>
                        {category.description}
                      </p>

                      {/* Hover Arrow */}
                      <div className={`
                        flex items-center justify-center mt-3
                        transition-all duration-300 ease-out
                        ${hoveredCategory === category.id ? 'opacity-100 transform translate-x-0' : 'opacity-0 transform translate-x-4'}
                      `}>
                        <span className="text-white text-sm font-medium mr-2">Shop Now</span>
                        <FaChevronRight className="text-white text-sm" />
                      </div>
                    </div>
                  </div>

                  {/* Hover Overlay */}
                  <div className={`
                    absolute inset-0 bg-black/10 transition-opacity duration-300
                    ${hoveredCategory === category.id ? 'opacity-100' : 'opacity-0'}
                  `}></div>
                </div>
              </Link>
            )
          })}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto border border-gray-100">
            <FaPaw className="text-[#575CEE] text-4xl mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              Can't Find What You're Looking For?
            </h3>
            <p className="text-gray-600 mb-6">
              Browse our complete collection of pet products and accessories
            </p>
            <Link
              to="/shopview"
              className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-[#575CEE] to-purple-600 text-white font-semibold rounded-full hover:from-[#4a4fd1] hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-[#575CEE] focus:ring-opacity-50"
            >
              <span>View All Products</span>
              <FaChevronRight className="ml-2 text-sm" />
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

export default PetCategories
