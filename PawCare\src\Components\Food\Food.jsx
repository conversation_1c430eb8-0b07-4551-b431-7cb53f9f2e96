import React from 'react'
import EnhancedShopLayout from '../Shop/EnhancedShopLayout'

const Food = () => {
  // Product data for dog food (ID range: 4000-4999)
  const dogFood = [
    {
      id: 4001,
      title: 'Premium Dry Dog Food',
      img: 'https://www.pngplay.com/wp-content/uploads/6/Drools-Dog-Food-Transparent-PNG.png',
      price: '$49.99',
      description: 'High-quality dry food for adult dogs'
    },
    {
      id: 4002,
      title: 'Puppy Formula',
      img: 'https://wallpapers.com/images/hd/gourmet-dog-food-png-unt-2gigx2pk2bcs5hv2.png',
      price: '$54.99',
      description: 'Specially formulated for growing puppies'
    },
    {
      id: 4003,
      title: 'Senior Dog Food',
      img: 'https://pngdow.com/public/uploads/thumbnail/dog-food-png-image-with-transparent-background-for-free-dog-food-4-11688836734cuufefecnv.png',
      price: '$52.99',
      description: 'Formulated for senior dogs'
    },
    {
      id: 4004,
      title: 'Grain-Free Dog Food',
      img: 'https://wallpapers.com/images/hd/pedigree-dog-food-lamb-rice-flavor-package-9shrk3548vuon0n7.png',
      price: '$59.99',
      description: 'Grain-free formula for sensitive dogs'
    },
    {
      id: 4005,
      title: 'Wet Dog Food',
      img: 'https://wallpapers.com/images/hd/air-dried-dog-food-png-05252024-0175lm63mctwk3t1.png',
      price: '$24.99',
      description: 'Premium wet food for all breeds'
    }
  ];

  // Product data for cat food (ID range: 5000-5999)
  const catFood = [
    {
      id: 5001,
      title: 'Indoor Cat Formula',
      img: 'https://i5.walmartimages.com/asr/79bfdf3b-d48d-42f0-90d0-ac297855cae9_3.3926583857bb0301951b5019a994f582.png',
      price: '$39.99',
      description: 'Specially formulated for indoor cats'
    },
    {
      id: 5002,
      title: 'Kitten Food',
      img: 'https://i5.walmartimages.com/asr/f413ea66-ec7c-4582-ab00-e526decdd200_3.03718d2e9c3df95295bd4f5fad91e1a6.png',
      price: '$42.99',
      description: 'Nutrient-rich food for growing kittens'
    },
    {
      id: 5003,
      title: 'Senior Cat Food',
      img: 'https://content.syndigo.com/asset/f3e5841e-8b4b-4800-933b-73aeb0cf2fd5/960.png',
      price: '$44.99',
      description: 'Easy-to-digest formula for senior cats'
    },
    {
      id: 5004,
      title: 'Wet Cat Food',
      img: 'https://1.bp.blogspot.com/-OIpl3PFqXvY/UqXrcnyahWI/AAAAAAAACco/yx4u4wAUGhA/s1600/Bili+Ka+Khana.png',
      price: '$22.99',
      description: 'Premium wet food for all cats'
    },
    {
      id: 5005,
      title: 'Grain-Free Cat Food',
      img: 'http://www.petsense.com/cdn/shop/files/888641133765_1_grande.png?v=1710869984',
      price: '$47.99',
      description: 'Grain-free formula for sensitive cats'
    }
  ];

  // Product data for specialty food (ID range: 12000-12999)
  const specialtyFood = [
    {
      id: 12001,
      title: 'Weight Management',
      img: 'https://fastpng.com/images/file/dog-food-png-847-x-861-oy86sgw8w11gljm4.png',
      price: '$54.99',
      description: 'For pets that need weight control'
    },
    {
      id: 12002,
      title: 'Sensitive Stomach',
      img: 'https://diamondpet.storage.googleapis.com/wp-content/uploads/2023/07/12102334/image6.png',
      price: '$56.99',
      description: 'Easy to digest for sensitive pets'
    },
    {
      id: 12003,
      title: 'Allergy Formula',
      img: 'https://www.masterfeeds.com/wp-content/uploads/NewPetFoodBags.png',
      price: '$59.99',
      description: 'For pets with food allergies'
    },
    {
      id: 12004,
      title: 'Organic Pet Food',
      img: 'http://www.havesippywilltravel.com/wp-content/uploads/2015/04/pic81.png',
      price: '$64.99',
      description: 'Made with organic ingredients'
    }
  ];

  // Combine all food products with enhanced data
  const allFoodProducts = [
    ...dogFood.map(product => ({
      ...product,
      category: 'dog-food',
      rating: 4.5 + Math.random() * 0.5,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.2).toFixed(2)}` : null
    })),
    ...catFood.map(product => ({
      ...product,
      category: 'cat-food',
      rating: 4.3 + Math.random() * 0.7,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.15).toFixed(2)}` : null
    })),
    ...specialtyFood.map(product => ({
      ...product,
      category: 'specialty-food',
      rating: 4.6 + Math.random() * 0.4,
      isNew: Math.random() > 0.6,
      isSale: Math.random() > 0.8,
      originalPrice: Math.random() > 0.6 ? `$${(parseFloat(product.price.replace('$', '')) * 1.25).toFixed(2)}` : null
    }))
  ]

  return (
    <>
      <EnhancedShopLayout
        products={allFoodProducts}
        title="Pet Food & Nutrition"
        heroImage="https://images.unsplash.com/photo-1589924691995-400dc9ecc119?w=800&h=600&fit=crop&crop=center"
        heroTitle="Premium Pet Food"
        heroDescription="Nutritious and delicious food for your beloved pets. From premium dry food to specialty diets, we have everything your pet needs for a healthy and happy life."
        showFilters={true}
        showSearch={true}
        showSorting={true}
        showViewToggle={true}
        categories={['dog-food', 'cat-food', 'specialty-food']}
      />
    </>
  )
}

export default Food
