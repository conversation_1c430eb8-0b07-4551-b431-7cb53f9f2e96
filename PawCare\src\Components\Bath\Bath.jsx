import React from 'react'
import EnhancedShopLayout from '../Shop/EnhancedShopLayout'

const Bath = () => {

    const dogBath = [
        { id: 1, title: 'Dog Bath', img: 'https://shop.welovedoodles.com/cdn/shop/files/206246_SENSITIVESKINSHAMPFront.png?v=1712797056', price: '$100', description: 'Premium dog bath for your furry friend' },
       { id: 2, title: 'Dog Bath', img:'https://i5.walmartimages.com/asr/1a1c1a99-55bc-43e4-a643-463bbfe68fed_1.d5ebcb72040fbf562f1a75e632be559e.png', price: '$100', description: 'Premium dog bath for your furry friend' },
       { id: 3, title: 'Dog Bath', img:'https://dogtime.com/wp-content/uploads/sites/12/2024/06/wags-wiggles_dog_shampoo.png', price: '$100', description: 'Premium dog bath for your furry friend' },
       { id: 4, title: 'Dog Bath', img:'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=400&fit=crop&crop=center', price: '$100', description: 'Premium dog bath for your furry friend' },
       { id: 5, title: 'Dog Bath', img:'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400&h=400&fit=crop&crop=center', price: '$100', description: 'Premium dog bath for your furry friend' },
    ]

const catBath = [
    { id: 11, title: 'Cat Bath', img: 'https://luxo-pet.com/wp-content/uploads/2023/05/Beep-CatCare-Shampo.png', price: '$100', description: 'Premium cat bath for your furry friend' },
    { id: 12, title: 'Cat Bath', img: 'https://miraclecarepet.com/wp-content/uploads/2020/04/11004_NatFleaTickShampooCat_16ozF.png', price: '$100', description: 'Premium cat bath for your furry friend' },
    { id: 13, title: 'Cat Bath', img: 'https://stratfordrx.com/wp-content/uploads/2022/12/SP1068-Tearless-Shampoo-16oz.png', price: '$100', description: 'Premium cat bath for your furry friend' },
    { id: 14, title: 'Cat Bath', img: 'https://i5.walmartimages.com/asr/7e2f405f-4ef7-4a28-ad49-4a94499a2133_1.672b4e481baa094e0b786eda4124a3cb.png', price: '$100', description: 'Premium cat bath for your furry friend' },
    { id: 15, title: 'Cat Bath', img: 'https://petpro.tropiclean.com/wp-content/uploads/2019/04/TC_OXY_Product-Photo_Medicated-Oatmeal-Shampoo_20oz_FRONT-683x1024.png', price: '$100', description: 'Premium cat bath for your furry friend' },

]

const specialtyBath = [

    { id: 21, title: 'Specialty Bath', img: 'https://zoomagazintiger.com/media/7/16175.png', price: '$100', description: 'Premium bath for your furry friend' },
    { id: 22, title: 'Specialty Bath', img: 'https://www.doggyfriend.com/assets/order_images/1284451(3).png', price: '$100', description: 'Premium bath for your furry friend' },
    { id: 23, title: 'Specialty Bath', img: 'https://sergeants.com/wp-content/uploads/2021/02/00073091001027-P000181-1_FRNT.png', price: '$100', description: 'Premium bath for your furry friend' },
    { id: 24, title: 'Specialty Bath', img: 'https://www.petfoodnmore.com/wp-content/uploads/2019/06/zodiac-flea-tic-shampoo-240ml-lg.png', price: '$100', description: 'Premium bath for your furry friend' },
    { id: 25, title: 'Specialty Bath', img: 'https://bluegold.garden/wp-content/uploads/2013/06/Dog-Shampoo-Mockup.png', price: '$100', description: 'Premium bath for your furry friend' },
]

  // Combine all bath products with enhanced data
  const allBathProducts = [
    ...dogBath.map(product => ({
      ...product,
      category: 'dog-bath',
      rating: 4.3 + Math.random() * 0.7,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.2).toFixed(2)}` : null
    })),
    ...catBath.map(product => ({
      ...product,
      category: 'cat-bath',
      rating: 4.4 + Math.random() * 0.6,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.15).toFixed(2)}` : null
    })),
    ...specialtyBath.map(product => ({
      ...product,
      category: 'specialty-bath',
      rating: 4.5 + Math.random() * 0.5,
      isNew: Math.random() > 0.6,
      isSale: Math.random() > 0.8,
      originalPrice: Math.random() > 0.6 ? `$${(parseFloat(product.price.replace('$', '')) * 1.25).toFixed(2)}` : null
    }))
  ]

  return (
    <>
      <EnhancedShopLayout
        products={allBathProducts}
        title="Bath & Grooming"
        heroImage="https://images.unsplash.com/photo-1576201836106-db1758fd1c97?w=800&h=600&fit=crop&crop=center"
        heroTitle="Premium Grooming Products"
        heroDescription="Find the perfect bath and grooming products for your furry friend. We offer a wide selection of high-quality shampoos, conditioners, and grooming supplies for dogs and cats of all sizes."
        showFilters={true}
        showSearch={true}
        showSorting={true}
        showViewToggle={true}
        categories={['dog-bath', 'cat-bath', 'specialty-bath']}
      />
    </>
  )
}

export default Bath
