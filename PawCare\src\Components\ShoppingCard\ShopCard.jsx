
import React, { useState } from 'react'
import { FaShoppingCart, FaHeart, FaEye, FaStar, FaCheck } from 'react-icons/fa'
import { useCart } from '../../context/CartContext'

function ShopCard({ id, title, img, price, description, rating = 4.5, originalPrice, isNew = false, isSale = false }) {
  const { addToCart } = useCart();
  const [isHovered, setIsHovered] = useState(false);
  const [isAddedToCart, setIsAddedToCart] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Add to cart function with visual feedback
  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();

    addToCart({ id, title, img, price, description });

    // Show success feedback
    setIsAddedToCart(true);
    setTimeout(() => setIsAddedToCart(false), 2000);
  };

  // Handle quick view (placeholder for future implementation)
  const handleQuickView = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Quick view for product:', id);
    // TODO: Implement quick view modal
  };

  // Handle wishlist (placeholder for future implementation)
  const handleWishlist = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Add to wishlist:', id);
    // TODO: Implement wishlist functionality
  };

  // Calculate discount percentage
  const discountPercentage = originalPrice ?
    Math.round(((parseFloat(originalPrice.replace('$', '')) - parseFloat(price.replace('$', ''))) / parseFloat(originalPrice.replace('$', ''))) * 100) : 0;

  return (
    <div
      className="group relative bg-white rounded-xl shadow-md hover:shadow-2xl transition-all duration-500 ease-out transform hover:-translate-y-2 overflow-hidden"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      role="article"
      aria-label={`${title} - ${price}`}
    >
      {/* Product Badges */}
      <div className="absolute top-3 left-3 z-10 flex flex-col gap-2">
        {isNew && (
          <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
            NEW
          </span>
        )}
        {isSale && discountPercentage > 0 && (
          <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
            -{discountPercentage}%
          </span>
        )}
      </div>

      {/* Wishlist Button */}
      <button
        onClick={handleWishlist}
        className={`absolute top-3 right-3 z-10 p-2 rounded-full transition-all duration-300 ${
          isHovered ? 'bg-white shadow-lg scale-110' : 'bg-white/80'
        }`}
        aria-label="Add to wishlist"
      >
        <FaHeart className="text-gray-400 hover:text-red-500 transition-colors duration-300" />
      </button>

      {/* Product Image Container */}
      <div className="relative h-64 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
        {!imageLoaded && !imageError && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#575CEE]"></div>
          </div>
        )}

        {imageError ? (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center text-gray-400">
              <div className="text-4xl mb-2">📦</div>
              <div className="text-sm">Image not available</div>
            </div>
          </div>
        ) : (
          <img
            className={`w-full h-full object-contain p-4 transition-all duration-500 ${
              isHovered ? 'scale-110' : 'scale-100'
            } ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            src={img}
            alt={title}
            onLoad={() => setImageLoaded(true)}
            onError={() => setImageError(true)}
            loading="lazy"
          />
        )}

        {/* Overlay Actions */}
        <div className={`absolute inset-0 bg-black/20 flex items-center justify-center transition-all duration-300 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}>
          <button
            onClick={handleQuickView}
            className="bg-white text-[#575CEE] px-4 py-2 rounded-full font-medium shadow-lg hover:bg-[#575CEE] hover:text-white transition-all duration-300 transform hover:scale-105"
            aria-label="Quick view product"
          >
            <FaEye className="inline mr-2" />
            Quick View
          </button>
        </div>
      </div>

      {/* Product Information */}
      <div className="p-4 flex flex-col flex-grow">
        {/* Rating */}
        <div className="flex items-center mb-2">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <FaStar
                key={i}
                className={`text-sm ${
                  i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="text-xs text-gray-500 ml-2">({rating})</span>
        </div>

        {/* Product Title */}
        <h3 className="font-bold text-lg mb-2 text-gray-900 line-clamp-2 group-hover:text-[#575CEE] transition-colors duration-300">
          {title}
        </h3>

        {/* Product Description */}
        {description && (
          <p className="text-gray-600 text-sm mb-3 flex-grow line-clamp-2 leading-relaxed">
            {description}
          </p>
        )}

        {/* Price Section */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <span className="font-bold text-xl text-[#575CEE]">{price}</span>
            {originalPrice && (
              <span className="text-sm text-gray-400 line-through">{originalPrice}</span>
            )}
          </div>
        </div>

        {/* Add to Cart Button */}
        <button
          onClick={handleAddToCart}
          disabled={isAddedToCart}
          className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-[#575CEE]/50 ${
            isAddedToCart
              ? 'bg-green-500 text-white'
              : 'bg-[#575CEE] hover:bg-[#4a4fd1] text-white hover:shadow-lg'
          }`}
          aria-label={isAddedToCart ? 'Added to cart' : 'Add to cart'}
        >
          <div className="flex items-center justify-center gap-2">
            {isAddedToCart ? (
              <>
                <FaCheck className="text-sm" />
                <span>Added to Cart!</span>
              </>
            ) : (
              <>
                <FaShoppingCart className="text-sm" />
                <span>Add to Cart</span>
              </>
            )}
          </div>
        </button>
      </div>

      {/* Hover Border Effect */}
      <div className={`absolute inset-0 border-2 border-[#575CEE] rounded-xl transition-opacity duration-300 pointer-events-none ${
        isHovered ? 'opacity-100' : 'opacity-0'
      }`}></div>
    </div>
  )
}

export default ShopCard
