
import React from 'react'
import EnhancedShopLayout from '../Shop/EnhancedShopLayout'

const CatShop = () => {
    const shopProducts = [
        { id: 14001, title: 'Cat Toy', img: 'https://m.media-amazon.com/images/I/61bf1yoHyKL._AC_SL1100_.jpg', price: '$100', description: 'Premium cat toys for your furry friend' },
        { id: 14002, title: 'Cat Toy', img: 'https://tse4.mm.bing.net/th?id=OIP.kJCBt1ZqHj5HmX1o2mFb_QHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat toys for your furry friend' },
        { id: 14003, title: 'Cat Toy', img: 'https://m.media-amazon.com/images/I/81kT2raqINL.jpg', price: '$100', description: 'Premium cat toys for your furry friend' },
        { id: 14004, title: 'Cat Toy', img:'https://tse3.mm.bing.net/th?id=OIP.8g6Ff_L8td8hjkKYnsjDggAAAA&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog toys for your furry friend' },
        { id: 14005, title: 'Cat Toy', img: 'https://tse1.mm.bing.net/th?id=OIP.mfsqt6EXFOo-6woRIS859AHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat toys for your furry friend' },
        { id: 14006, title: 'Cat Toy', img: 'https://m.media-amazon.com/images/I/81SyIhbxlOL._AC_SL1500_.jpg', price: '$100', description: 'Premium cat toys for your furry friend' },
        { id: 14007, title: 'Cat Toy', img: 'https://images-na.ssl-images-amazon.com/images/I/51qY3tUBtOL._AC_SL1001_.jpg', price: '$100', description: 'Premium cat toys for your furry friend' },

      ];

      const FoodProducts =[
        { id: 14008, title: 'Cat Food', img: 'https://tse1.mm.bing.net/th?id=OIP.P3FMcOoc19cqDtF80-LKOgHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 14009, title: 'Cat Food', img: 'https://i5.walmartimages.com/asr/2700f3bb-06f4-4aa3-b23e-cbf54775204b.4a2607976c1debf4c8efe8838aaae30f.jpeg', price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 14010, title: 'Cat Food', img: 'https://tse2.mm.bing.net/th?id=OIP.8Fn7qeOVf_JOQ4zijV_09QHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 14011, title: 'Cat Food', img: 'https://www.catfoodsadvisor.com/wp-content/uploads/2020/04/3-45.jpg', price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 14012, title: 'Cat Food', img: 'https://tse1.mm.bing.net/th?id=OIP.EpTu73wMuVt1WeEL2kEBjQHaHa&pid=Api&P=0&h=220' , price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 14013, title: 'Cat Food', img: 'https://i5.walmartimages.com/asr/2be6df13-1c3f-4f06-a8f5-36f16024165a.cf9b5f482403cfd3468b9a64ac8bce6a.jpeg', price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 14014, title: 'Cat Food', img: 'https://tse2.mm.bing.net/th?id=OIP.5TDhdyPZpUY4i17RQMCtBwHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium Cat food for your furry friend' },
      ]

      const Furniture = [

        { id: 14015, title:'Cat  House', img: 'https://tse3.mm.bing.net/th?id=OIP.dAXSehySWdmu-K1ouopIRwHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat bed for your furry friend' },
        { id: 14016, title:'Cat  Bed', img: 'https://images-na.ssl-images-amazon.com/images/I/618SNkdn8IL._AC_SX466_.jpg', price: '$100', description: 'Premium cat bed for your furry friend' },
        {id:14017 , title:'Cat sofa', img:'https://tse1.mm.bing.net/th?id=OIP.aXhE0l4HkmmNArQSAoSvKwHaGc&pid=Api&P=0&h=220', price:'$100', description:'Premium cat bed for your furry friend' },
        {id:14018 , title:'Cat Bath tub', img:'https://tse4.mm.bing.net/th?id=OIP.POnBBzWQNcp020uQAdz1aAHaFL&pid=Api&P=0&h=220', price:'$100', description:'Premium cat bed for your furry friend' },
        {id:14019 , title:'Cat Grooming Table',img:'https://i.ebayimg.com/images/g/CJAAAOSwFytjyOGw/s-l500.jpg', price:'$100', description:'Premium cat bed for your furry friend' },
        {id:14020 , title:'Cat Collar', img:'https://tse1.mm.bing.net/th?id=OIP.g6nLaNm3XxEcqd0PWL__AgHaH1&pid=Api&P=0&h=220', price:'$100', description:'Premium cat bed for your furry friend' },
        {id:14021 , title:'Cat Nail Clipper', img:'https://m.media-amazon.com/images/I/71GAKn+w23L._AC_SL1500_.jpg', price:'$100', description:'Premium cat bed for your furry friend' },
      ]

      const Grooming = [
        { id: 14022, title: 'Cat Grooming', img:'https://tse2.mm.bing.net/th?id=OIP.nvfO3NBVs0fjRUTNj9q3_AHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 14023, title: 'Cat Grooming', img:'https://m.media-amazon.com/images/I/71Cj4f2aj-L._AC_SX569_.jpg', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 14024, title: 'Cat Grooming', img:'https://cdn-fastly.petguide.com/media/2022/03/02/8296264/best-cat-grooming-products.jpg?size=720x845&nocrop=1', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 14025, title: 'Cat Grooming', img:'https://tse4.mm.bing.net/th?id=OIP.-4tdEfAmjE2p-Py0fmjOogHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 14026, title: 'Cat Grooming', img:'https://tse4.mm.bing.net/th?id=OIP.bGGEDWhgGUUgbUgg8C7U3AHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 14027, title: 'Cat Grooming', img:'https://tse3.mm.bing.net/th?id=OIP.pLbg8w1sCCTh9HVAtLuWCwHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 14028, title: 'Cat Grooming', img:'https://tse1.mm.bing.net/th?id=OIP.WXSvp3ND1hHoPxPyftYZfwHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat grooming for your furry friend' },
       ]

  // Combine all cat products with enhanced data
  const allCatProducts = [
    ...shopProducts.map(product => ({
      ...product,
      category: 'cat-toys',
      rating: 4.4 + Math.random() * 0.6,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.2).toFixed(2)}` : null
    })),
    ...FoodProducts.map(product => ({
      ...product,
      category: 'cat-food',
      rating: 4.5 + Math.random() * 0.5,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.15).toFixed(2)}` : null
    })),
    ...Furniture.map(product => ({
      ...product,
      category: 'cat-furniture',
      rating: 4.3 + Math.random() * 0.7,
      isNew: Math.random() > 0.6,
      isSale: Math.random() > 0.8,
      originalPrice: Math.random() > 0.6 ? `$${(parseFloat(product.price.replace('$', '')) * 1.25).toFixed(2)}` : null
    })),
    ...Grooming.map(product => ({
      ...product,
      category: 'cat-grooming',
      rating: 4.4 + Math.random() * 0.6,
      isNew: Math.random() > 0.7,
      isSale: Math.random() > 0.8,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.2).toFixed(2)}` : null
    }))
  ]

      return (
        <>
          <EnhancedShopLayout
            products={allCatProducts}
            title="Cat Shop - Everything for Cats"
            heroImage="https://images.unsplash.com/photo-1574144611937-0df059b5ef3e?w=800&h=600&fit=crop&crop=center"
            heroTitle="Everything Your Cat Needs"
            heroDescription="Discover our comprehensive collection of cat products including toys, food, furniture, and grooming supplies. Everything to keep your feline friend happy and healthy."
            showFilters={true}
            showSearch={true}
            showSorting={true}
            showViewToggle={true}
            categories={['cat-toys', 'cat-food', 'cat-furniture', 'cat-grooming']}
          />
        </>
      )

}

export default CatShop
