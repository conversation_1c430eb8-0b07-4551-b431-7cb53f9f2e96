
import React from 'react'
import EnhancedShopLayout from '../Shop/EnhancedShopLayout'


const Toys = () => {

    const dogToys = [
        { id: 10001, title: 'Dog Toy', img: 'https://www.pngkey.com/png/full/305-3057207_toy1-dog-toy-transparent-background.png', price: '$100', description: 'Premium dog toys for your furry friend' },
       { id: 10002, title: 'Dog Toy', img:'https://www.pngkey.com/png/full/305-3056551_2-knot-tug-o-war-rope-dog-toy.png', price: '$100', description: 'Premium dog toys for your furry friend' },
       { id: 10003, title: 'Dog Toy', img:'https://www.seekpng.com/png/full/305-3057153_dog-toy-png.png', price: '$100', description: 'Premium dog toys for your furry friend' },
       { id: 10004, title: 'Dog Toy', img:'https://wallpapers.com/images/hd/dog-toy-for-fetching-png-nhj-32k9afj73dxgr683.png', price: '$100', description: 'Premium dog toys for your furry friend' },
       { id: 10005, title: 'Dog Toy', img:'https://png.pngtree.com/png-vector/20231115/ourmid/pngtree-dog-toy-white-background-dog-png-image_10592746.png', price: '$100', description: 'Premium dog toys for your furry friend' },
    ]

const catToys = [
    { id: 11001, title: 'Cat Toy', img: 'https://wallpapers.com/images/hd/cat-toy-with-feathers-png-njl41-rap7w2y926xqhj9f.png', price: '$100', description: 'Premium cat toys for your furry friend' },
    { id: 11002, title: 'Cat Toy', img: 'https://wallpapers.com/images/hd/cat-toy-with-feathers-png-10-wa2yfq3p5tdykvqe.png', price: '$100', description: 'Premium cat toys for your furry friend' },
    { id: 11003, title: 'Cat Toy', img: 'https://www.pikpng.com/pngl/b/591-5913245_cat-toy-png.png', price: '$100', description: 'Premium cat toys for your furry friend' },
    { id: 11004, title: 'Cat Toy', img: 'https://pngpix.com/images/hd/ball-tower-cat-toy-png-68-zuggx452cqoycue1.jpg', price: '$100', description: 'Premium cat toys for your furry friend' },
    { id: 11005, title: 'Cat Toy', img: 'https://wallpapers.com/images/hd/swing-ball-cat-toy-png-06212024-9wbec2wmrhke6dxb.png', price: '$100', description: 'Premium cat toys for your furry friend' },

]

const specialtyToys = [

    { id: 12013, title: 'Specialty Toy', img: 'https://tse4.mm.bing.net/th?id=OIP.LlGmxmZlxV-OSINCDdoOdwHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium specialty toys for your furry friend' },
    { id: 12014, title: 'Specialty Toy', img: 'https://png.pngtree.com/png-vector/20240103/ourmid/pngtree-dog-toy-on-white-plaything-png-image_11263090.png', price: '$100', description: 'Premium specialty toys for your furry friend' },
    { id: 12015, title: 'Specialty Toy', img: 'https://png.pngtree.com/png-vector/20240104/ourmid/pngtree-dog-toy-chewing-png-image_11174327.png', price: '$100', description: 'Premium specialty toys for your furry friend' },
    { id: 12016, title: 'Specialty Toy', img: 'https://www.pngkey.com/png/full/305-3056807_dog-toys.png', price: '$100', description: 'Premium specialty toys for your furry friend' },
    { id: 12017, title: 'Specialty Toy', img: 'https://wallpapers.com/images/hd/dog-toy-ball-png-89-nxw42hazlqkztghh.png', price: '$100', description: 'Premium specialty toys for your furry friend' },
]

  // Combine all toy products with enhanced data
  const allToyProducts = [
    ...dogToys.map(product => ({
      ...product,
      category: 'dog-toys',
      rating: 4.4 + Math.random() * 0.6,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.3).toFixed(2)}` : null
    })),
    ...catToys.map(product => ({
      ...product,
      category: 'cat-toys',
      rating: 4.3 + Math.random() * 0.7,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.25).toFixed(2)}` : null
    })),
    ...specialtyToys.map(product => ({
      ...product,
      category: 'specialty-toys',
      rating: 4.6 + Math.random() * 0.4,
      isNew: Math.random() > 0.6,
      isSale: Math.random() > 0.8,
      originalPrice: Math.random() > 0.6 ? `$${(parseFloat(product.price.replace('$', '')) * 1.2).toFixed(2)}` : null
    }))
  ]

  return (
    <>
      <EnhancedShopLayout
        products={allToyProducts}
        title="Pet Toys & Play"
        heroImage="https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=800&h=600&fit=crop&crop=center"
        heroTitle="Fun Toys for Happy Pets"
        heroDescription="Discover our amazing collection of toys that will keep your pets entertained, active, and happy. From interactive puzzles to chew toys, we have something for every pet."
        showFilters={true}
        showSearch={true}
        showSorting={true}
        showViewToggle={true}
        categories={['dog-toys', 'cat-toys', 'specialty-toys']}
      />
    </>
  )
}

export default Toys
