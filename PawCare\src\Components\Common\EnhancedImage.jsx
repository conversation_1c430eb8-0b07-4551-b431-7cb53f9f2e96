import React, { useState, useEffect } from 'react';
import { getFallbackImage, createPlaceholderImage, optimizeImageUrl } from '../../utils/imageUtils';

/**
 * Enhanced Image component with fallback handling, loading states, and optimization
 */
const EnhancedImage = ({
  src,
  alt,
  category = 'default',
  className = '',
  fallbackSrc = null,
  showLoadingSpinner = true,
  retryAttempts = 2,
  optimizeUrl = true,
  ...props
}) => {
  const [currentSrc, setCurrentSrc] = useState(src);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Optimize the image URL if requested
  const optimizedSrc = optimizeUrl ? optimizeImageUrl(currentSrc) : currentSrc;

  useEffect(() => {
    setLoading(true);
    setError(false);
    setRetryCount(0);
    setCurrentSrc(src);
  }, [src]);

  const handleImageLoad = () => {
    setLoading(false);
    setError(false);
  };

  const handleImageError = () => {
    if (retryCount < retryAttempts) {
      // Retry with exponential backoff
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        setLoading(true);
        setError(false);
      }, 1000 * Math.pow(2, retryCount));
    } else {
      // Use fallback image
      setLoading(false);
      setError(true);
      
      const fallback = fallbackSrc || getFallbackImage(category);
      if (currentSrc !== fallback) {
        setCurrentSrc(fallback);
        setRetryCount(0);
        setLoading(true);
        setError(false);
      }
    }
  };

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Loading Spinner */}
      {loading && showLoadingSpinner && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#575CEE]"></div>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
          <div className="text-center text-gray-500">
            <div className="text-4xl mb-2">🐾</div>
            <div className="text-sm font-medium">Image Unavailable</div>
          </div>
        </div>
      )}

      {/* Actual Image */}
      <img
        key={`${currentSrc}-${retryCount}`} // Force re-render on retry
        src={optimizedSrc}
        alt={alt}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          loading ? 'opacity-0' : 'opacity-100'
        }`}
        onLoad={handleImageLoad}
        onError={handleImageError}
        loading="lazy"
        {...props}
      />
    </div>
  );
};

export default EnhancedImage;
