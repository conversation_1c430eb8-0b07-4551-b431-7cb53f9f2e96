import React from 'react'
import EnhancedShopLayout from '../Shop/EnhancedShopLayout'

const Treats = () => {
  // Product data for dog treats (ID range: 6000-6999)
  const dogTreats = [
    {
      id: 6001,
      title: 'Dental Chews',
      img: 'https://clipground.com/images/dog-treats-png-5.png',
      price: '$14.99',
      description: 'Helps clean teeth and freshen breath'
    },
    {
      id: 6002,
      title: 'Training Treats',
      img: 'https://static.vecteezy.com/system/resources/thumbnails/047/067/444/small_2x/bone-shaped-dog-biscuit-treat-free-png.png',
      price: '$9.99',
      description: 'Small treats perfect for training'
    },
    {
      id: 6003,
      title: 'Jerky Treats',
      img: 'https://wallpapers.com/images/hd/senior-dog-treat-formula-png-14-oeutqviphea2fbce.png',
      price: '$16.99',
      description: 'Premium jerky treats for dogs'
    },
    {
      id: 6004,
      title: 'Biscuit Treats',
      img: 'https://pngpix.com/images/hd/natural-dog-treat-selection-png-swi-zp0cr6qaokgkweqq.jpg',
      price: '$12.99',
      description: 'Crunchy biscuits dogs love'
    },
    {
      id: 6005,
      title: 'Grain-Free Treats',
      img: 'https://www.poshaprani.com/wp-content/uploads/2019/08/pedigree-tasty-bites-chewy-slices-with-beef-155gm-874x1024.png',
      price: '$18.99',
      description: 'Grain-free treats for sensitive dogs'
    }
  ];

  // Product data for cat treats (ID range: 7000-7999)
  const catTreats = [
    {
      id: 7001,
      title: 'Crunchy Cat Treats',
      img: 'https://i5.walmartimages.com/asr/06d0a91e-d693-4164-bf44-cd261abec9c1_4.56e485c836132dc71d72d85f6371d317.png',
      price: '$8.99',
      description: 'Crunchy treats cats love'
    },
    {
      id: 7002,
      title: 'Soft Cat Treats',
      img: 'https://pacificpet.net/img/products/50089793.png',
      price: '$9.99',
      description: 'Soft and chewy treats for cats'
    },
    {
      id: 7003,
      title: 'Dental Cat Treats',
      img: 'https://i5.walmartimages.com/seo/BONKERS-Purrpops-Lollipop-Cat-Treats-Chicky-Licks-Flavor-4-Pack_74201d5e-0f96-4bdb-bc5f-43a6662b6ae6.4d11317f4cca4523731f90cf0bab2060.png',
      price: '$11.99',
      description: 'Helps maintain dental health'
    },
    {
      id: 7004,
      title: 'Catnip Treats',
      img: 'https://littlestinkers.ca/cdn/shop/products/FrommPurrSnackittySoft_SavoryCatTreats_85g_ChickenFlavor1.png?v=**********&width=416',
      price: '$10.99',
      description: 'Treats infused with catnip'
    },
    {
      id: 7005,
      title: 'Hairball Control Treats',
      img: 'https://cdn.shopify.com/s/files/1/0054/7320/6307/products/KitCat-KitCatPurrPuree-Chicken_FiberHariballControl_CatTreat.png?v=**********',
      price: '$12.99',
      description: 'Helps reduce hairballs'
    }
  ];

  // Product data for specialty treats (ID range: 12000-12999)
  const specialtyTreats = [
    {
      id: 12005,
      title: 'Calming Treats',
      img: 'https://tse1.mm.bing.net/th?id=OIP.1qeUdbRk4xfjG1GsIvXutgHaJQ&pid=Api&P=0&h=220',
      price: '$19.99',
      description: 'Helps reduce anxiety in pets'
    },
    {
      id: 12006,
      title: 'Joint Health Treats',
      img: 'https://www.k9bytesgifts.com/gallery/dog-treats-2021.jpg',
      price: '$21.99',
      description: 'Supports joint health and mobility'
    },
    {
      id: 12007,
      title: 'Organic Treats',
      img: 'https://roccotreats.com/wp-content/uploads/2023/05/Dog-Treats.png',
      price: '$16.99',
      description: 'Made with organic ingredients'
    },
    {
      id: 12008,
      title: 'Allergy-Free Treats',
      img: 'https://i5.walmartimages.com/asr/1dc3118d-65d3-4b8e-8a6e-3eb3edc573d0.5a5d1b4067d5df30ae10896449a2610a.png',
      price: '$17.99',
      description: 'For pets with food sensitivities'
    }
  ];

  // Combine all treat products with enhanced data
  const allTreatProducts = [
    ...dogTreats.map(product => ({
      ...product,
      category: 'dog-treats',
      rating: 4.5 + Math.random() * 0.5,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.2).toFixed(2)}` : null
    })),
    ...catTreats.map(product => ({
      ...product,
      category: 'cat-treats',
      rating: 4.4 + Math.random() * 0.6,
      isNew: Math.random() > 0.8,
      isSale: Math.random() > 0.7,
      originalPrice: Math.random() > 0.7 ? `$${(parseFloat(product.price.replace('$', '')) * 1.15).toFixed(2)}` : null
    })),
    ...specialtyTreats.map(product => ({
      ...product,
      category: 'specialty-treats',
      rating: 4.6 + Math.random() * 0.4,
      isNew: Math.random() > 0.6,
      isSale: Math.random() > 0.8,
      originalPrice: Math.random() > 0.6 ? `$${(parseFloat(product.price.replace('$', '')) * 1.25).toFixed(2)}` : null
    }))
  ]

  return (
    <>
      <EnhancedShopLayout
        products={allTreatProducts}
        title="Pet Treats & Snacks"
        heroImage="https://www.schmackos.com.au/sites/g/files/fnmzdf4436/files/2023-10/Schmackos_Rebrand%202022_Website_Assets_Pack%20Render%20Everyday%20-%20425x425px%20-%20Strapz%20Chicken.png"
        heroTitle="Delicious Pet Treats"
        heroDescription="Reward your furry friends with our delicious and healthy treats. Perfect for training, rewarding, or just showing your pet some extra love."
        showFilters={true}
        showSearch={true}
        showSorting={true}
        showViewToggle={true}
        categories={['dog-treats', 'cat-treats', 'specialty-treats']}
      />
    </>
  )
}

export default Treats
