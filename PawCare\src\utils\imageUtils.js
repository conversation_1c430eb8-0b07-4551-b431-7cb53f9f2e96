/**
 * Image utility functions for PawCare application
 * Handles image validation, fallbacks, and optimization
 */

// Default fallback images for different categories
export const FALLBACK_IMAGES = {
  'dog-food': 'https://images.unsplash.com/photo-1589924691995-400dc9ecc119?w=400&h=400&fit=crop&crop=center',
  'cat-food': 'https://images.unsplash.com/photo-1548681528-6a5c45b66b42?w=400&h=400&fit=crop&crop=center',
  'dog-toys': 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=400&fit=crop&crop=center',
  'cat-toys': 'https://images.unsplash.com/photo-1573865526739-10659fec78a5?w=400&h=400&fit=crop&crop=center',
  'dog-beds': 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400&h=400&fit=crop&crop=center',
  'cat-beds': 'https://images.unsplash.com/photo-1574144611937-0df059b5ef3e?w=400&h=400&fit=crop&crop=center',
  'dog-bath': 'https://images.unsplash.com/photo-1576201836106-db1758fd1c97?w=400&h=400&fit=crop&crop=center',
  'cat-bath': 'https://images.unsplash.com/photo-1574144611937-0df059b5ef3e?w=400&h=400&fit=crop&crop=center',
  'dog-treats': 'https://images.unsplash.com/photo-1605568427561-40dd23c2acea?w=400&h=400&fit=crop&crop=center',
  'cat-treats': 'https://images.unsplash.com/photo-1548681528-6a5c45b66b42?w=400&h=400&fit=crop&crop=center',
  'dog-furniture': 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400&h=400&fit=crop&crop=center',
  'cat-furniture': 'https://images.unsplash.com/photo-1574144611937-0df059b5ef3e?w=400&h=400&fit=crop&crop=center',
  'default': 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=400&fit=crop&crop=center'
};

// Hero images for different shop sections
export const HERO_IMAGES = {
  food: 'https://images.unsplash.com/photo-1589924691995-400dc9ecc119?w=800&h=600&fit=crop&crop=center',
  toys: 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=800&h=600&fit=crop&crop=center',
  beds: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=800&h=600&fit=crop&crop=center',
  bath: 'https://images.unsplash.com/photo-1576201836106-db1758fd1c97?w=800&h=600&fit=crop&crop=center',
  treats: 'https://images.unsplash.com/photo-1605568427561-40dd23c2acea?w=800&h=600&fit=crop&crop=center',
  furniture: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=800&h=600&fit=crop&crop=center',
  catshop: 'https://images.unsplash.com/photo-1574144611937-0df059b5ef3e?w=800&h=600&fit=crop&crop=center'
};

/**
 * Validates if an image URL is accessible
 * @param {string} url - The image URL to validate
 * @returns {Promise<boolean>} - True if image is accessible, false otherwise
 */
export const validateImageUrl = async (url) => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.warn('Image validation failed:', url, error);
    return false;
  }
};

/**
 * Gets a fallback image URL based on product category
 * @param {string} category - The product category
 * @returns {string} - Fallback image URL
 */
export const getFallbackImage = (category) => {
  return FALLBACK_IMAGES[category] || FALLBACK_IMAGES.default;
};

/**
 * Gets a hero image URL for a shop section
 * @param {string} section - The shop section name
 * @returns {string} - Hero image URL
 */
export const getHeroImage = (section) => {
  return HERO_IMAGES[section] || HERO_IMAGES.food;
};

/**
 * Optimizes image URL for better performance
 * @param {string} url - Original image URL
 * @param {Object} options - Optimization options
 * @returns {string} - Optimized image URL
 */
export const optimizeImageUrl = (url, options = {}) => {
  const { width = 400, height = 400, quality = 80 } = options;
  
  // If it's an Unsplash URL, add optimization parameters
  if (url.includes('unsplash.com')) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}w=${width}&h=${height}&q=${quality}&auto=format`;
  }
  
  // For other URLs, return as-is (could add other optimizations here)
  return url;
};

/**
 * Checks if an image URL is from a reliable source
 * @param {string} url - The image URL to check
 * @returns {boolean} - True if from a reliable source
 */
export const isReliableImageSource = (url) => {
  const reliableSources = [
    'unsplash.com',
    'images.unsplash.com',
    'cdn.shopify.com',
    'images.pexels.com',
    'm.media-amazon.com',
    'images-na.ssl-images-amazon.com'
  ];
  
  return reliableSources.some(source => url.includes(source));
};

/**
 * Replaces unreliable image URLs with fallback images
 * @param {Array} products - Array of product objects
 * @returns {Array} - Products with validated image URLs
 */
export const validateProductImages = (products) => {
  return products.map(product => {
    if (!product.img || !isReliableImageSource(product.img)) {
      return {
        ...product,
        img: getFallbackImage(product.category),
        originalImg: product.img // Keep original for reference
      };
    }
    return product;
  });
};

/**
 * Preloads an image to improve user experience
 * @param {string} url - Image URL to preload
 * @returns {Promise} - Promise that resolves when image is loaded
 */
export const preloadImage = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = url;
  });
};

/**
 * Creates a data URL for a placeholder image
 * @param {string} text - Text to display in placeholder
 * @param {Object} options - Placeholder options
 * @returns {string} - Data URL for placeholder image
 */
export const createPlaceholderImage = (text = '🐾', options = {}) => {
  const { width = 400, height = 400, bgColor = '#f3f4f6', textColor = '#6b7280' } = options;
  
  const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
      <rect width="${width}" height="${height}" fill="${bgColor}"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="48" fill="${textColor}" text-anchor="middle" dominant-baseline="middle">
        ${text}
      </text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

/**
 * Image loading hook for React components
 * @param {string} src - Image source URL
 * @param {string} fallback - Fallback image URL
 * @returns {Object} - Loading state and image source
 */
export const useImageLoader = (src, fallback) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [imageSrc, setImageSrc] = useState(src);
  
  useEffect(() => {
    setLoading(true);
    setError(false);
    
    const img = new Image();
    img.onload = () => {
      setLoading(false);
      setImageSrc(src);
    };
    img.onerror = () => {
      setLoading(false);
      setError(true);
      setImageSrc(fallback || createPlaceholderImage());
    };
    img.src = src;
  }, [src, fallback]);
  
  return { loading, error, imageSrc };
};
