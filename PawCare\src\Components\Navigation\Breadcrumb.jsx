import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { FaHome, FaChevronRight } from 'react-icons/fa'

const Breadcrumb = ({ customBreadcrumbs = null }) => {
  const location = useLocation()
  
  // Define route mappings for better breadcrumb names
  const routeNames = {
    '/': 'Home',
    '/home': 'Home',
    '/shopview': 'Shop',
    '/CatShop': 'Cat Shop',
    '/beds': 'Beds & Comfort',
    '/toys': 'Toys & Play',
    '/bath': 'Bath & Grooming',
    '/food': 'Food & Nutrition',
    '/treats': 'Treats & Snacks',
    '/furniture': 'Furniture & Accessories',
    '/about': 'About Us',
    '/vet-clinics': 'Vet Clinics',
    '/shelters': 'Pet Shelters',
    '/shelter-adoption': 'Pet Adoption',
    '/pet-guide': 'Pet Care Guide',
    '/cart': 'Shopping Cart',
    '/orders': 'My Orders',
    '/online-appointment': 'Book Appointment'
  }

  // Generate breadcrumbs from current path
  const generateBreadcrumbs = () => {
    if (customBreadcrumbs) {
      return customBreadcrumbs
    }

    const pathnames = location.pathname.split('/').filter(x => x)
    
    const breadcrumbs = [
      { name: 'Home', path: '/home', icon: FaHome }
    ]

    let currentPath = ''
    pathnames.forEach((pathname, index) => {
      currentPath += `/${pathname}`
      const isLast = index === pathnames.length - 1
      
      breadcrumbs.push({
        name: routeNames[currentPath] || pathname.charAt(0).toUpperCase() + pathname.slice(1),
        path: currentPath,
        isLast
      })
    })

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  // Don't show breadcrumbs on home page
  if (location.pathname === '/' || location.pathname === '/home') {
    return null
  }

  return (
    <nav 
      className="bg-white border-b border-gray-200 py-3"
      aria-label="Breadcrumb navigation"
    >
      <div className="container mx-auto px-4">
        <ol className="flex items-center space-x-2 text-sm">
          {breadcrumbs.map((breadcrumb, index) => {
            const IconComponent = breadcrumb.icon
            
            return (
              <li key={breadcrumb.path} className="flex items-center">
                {index > 0 && (
                  <FaChevronRight className="text-gray-400 mx-2 text-xs" />
                )}
                
                {breadcrumb.isLast ? (
                  <span 
                    className="text-[#575CEE] font-medium flex items-center"
                    aria-current="page"
                  >
                    {IconComponent && <IconComponent className="mr-2" />}
                    {breadcrumb.name}
                  </span>
                ) : (
                  <Link
                    to={breadcrumb.path}
                    className="text-gray-600 hover:text-[#575CEE] transition-colors duration-200 flex items-center hover:underline"
                  >
                    {IconComponent && <IconComponent className="mr-2" />}
                    {breadcrumb.name}
                  </Link>
                )}
              </li>
            )
          })}
        </ol>
      </div>
    </nav>
  )
}

export default Breadcrumb
